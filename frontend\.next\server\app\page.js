/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CMedicagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMedicagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CMedicagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMedicagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Medicagent\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\Medicagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Medicagent\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CMedicagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMedicagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNNZWRpY2FnZW50JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWlGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxNZWRpY2FnZW50XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxNZWRpY2FnZW50XFxmcm9udGVuZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d0ac5e14084b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcTWVkaWNhZ2VudFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQwYWM1ZTE0MDg0YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Medicagent: Multi-Agent Medical Assistant\",\n    description: \"A modern medical assistant application\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"mdl-js\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFVOzswQkFDeEIsOERBQUNDOzBCQUNDLDRFQUFDQztvQkFDQ0MsS0FBSTtvQkFDSkMsTUFBSzs7Ozs7Ozs7Ozs7MEJBR1QsOERBQUNDO2dCQUFLTCxXQUFXUiwrSkFBZTswQkFBR0s7Ozs7Ozs7Ozs7OztBQUd6QyIsInNvdXJjZXMiOlsiRDpcXE1lZGljYWdlbnRcXGZyb250ZW5kXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcclxuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xyXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XHJcblxyXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSk7XHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiBcIk1lZGljYWdlbnQ6IE11bHRpLUFnZW50IE1lZGljYWwgQXNzaXN0YW50XCIsXHJcbiAgZGVzY3JpcHRpb246IFwiQSBtb2Rlcm4gbWVkaWNhbCBhc3Npc3RhbnQgYXBwbGljYXRpb25cIixcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xyXG4gIGNoaWxkcmVuLFxyXG59OiBSZWFkb25seTx7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufT4pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgY2xhc3NOYW1lPVwibWRsLWpzXCI+XHJcbiAgICAgIDxoZWFkPlxyXG4gICAgICAgIDxsaW5rXHJcbiAgICAgICAgICByZWw9XCJzdHlsZXNoZWV0XCJcclxuICAgICAgICAgIGhyZWY9XCJodHRwczovL2NkbmpzLmNsb3VkZmxhcmUuY29tL2FqYXgvbGlicy9mb250LWF3ZXNvbWUvNi40LjAvY3NzL2FsbC5taW4uY3NzXCJcclxuICAgICAgICAvPlxyXG4gICAgICA8L2hlYWQ+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT57Y2hpbGRyZW59PC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJoZWFkIiwibGluayIsInJlbCIsImhyZWYiLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Medicagent\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Medicagent\\frontend\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNNZWRpY2FnZW50JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWlGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxNZWRpY2FnZW50XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CMedicagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_chat_Chat__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/chat/Chat */ \"(ssr)/./src/components/chat/Chat.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_Chat__WEBPACK_IMPORTED_MODULE_1__.Chat, {}, void 0, false, {\n            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRThDO0FBRS9CLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFLQyxXQUFVO2tCQUNkLDRFQUFDSCx1REFBSUE7Ozs7Ozs7Ozs7QUFHWCIsInNvdXJjZXMiOlsiRDpcXE1lZGljYWdlbnRcXGZyb250ZW5kXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgeyBDaGF0IH0gZnJvbSAnQC9jb21wb25lbnRzL2NoYXQvQ2hhdCc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8bWFpbiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctYmFja2dyb3VuZFwiPlxyXG4gICAgICA8Q2hhdCAvPlxyXG4gICAgPC9tYWluPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIkNoYXQiLCJIb21lIiwibWFpbiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/Chat.tsx":
/*!**************************************!*\
  !*** ./src/components/chat/Chat.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chat: () => (/* binding */ Chat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Message__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Message */ \"(ssr)/./src/components/chat/Message.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Paperclip_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Paperclip,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Paperclip_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Paperclip,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Paperclip_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Paperclip,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Paperclip_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Paperclip,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Paperclip_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Paperclip,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ Chat auto */ \n\n\n\n\n\n\n\nfunction Chat() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            role: 'assistant',\n            content: \"Chào bạn, tôi là hệ thống Medicagent. Tôi có chuyên môn trong 3 lĩnh vực chính: Khối u não, X-quang ngực, và phân vùng tổn thương da. Tuy nhiên, tôi chỉ đóng vai trò hỗ trợ và không thể thay thế được các chuyên gia y tế. Nếu có câu hỏi hoặc cần chẩn đoán, hãy gửi thông tin nhé, tôi sẽ giúp bạn tìm kiếm thông tin chính xác nhất!\"\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Chat.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"Chat.useEffect\"], [\n        messages\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!input.trim() && !selectedImage || isProcessing) return;\n        const newMessage = {\n            role: 'user',\n            content: input.trim(),\n            image: selectedImage || undefined\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setInput('');\n        setSelectedImage(null);\n        setIsProcessing(true);\n        try {\n            let response;\n            if (selectedImage) {\n                const imageFile = await fetch(selectedImage).then((r)=>r.blob());\n                response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.uploadImage)(new File([\n                    imageFile\n                ], 'image.jpg'), input.trim());\n            } else {\n                response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.sendChatMessage)(input.trim());\n            }\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        role: 'assistant',\n                        content: response.response,\n                        agent: response.agent,\n                        resultImage: response.result_image\n                    }\n                ]);\n        } catch (error) {\n            console.error('Error sending message:', error);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        role: 'system',\n                        content: 'Sorry, there was an error processing your request. Please try again.'\n                    }\n                ]);\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    const handleImageUpload = (event)=>{\n        const file = event.target.files?.[0];\n        if (file) {\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                setSelectedImage(e.target?.result);\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const handleValidation = async (validation, comments)=>{\n        setIsProcessing(true);\n        try {\n            const response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.sendValidation)(validation, comments);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        role: 'assistant',\n                        content: response.response,\n                        agent: 'HUMAN_VALIDATED'\n                    }\n                ]);\n        } catch (error) {\n            console.error('Error sending validation:', error);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        role: 'system',\n                        content: 'Sorry, there was an error processing your validation. Please try again.'\n                    }\n                ]);\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    const toggleRecording = async ()=>{\n        if (isRecording) {\n            mediaRecorderRef.current?.stop();\n            setIsRecording(false);\n            return;\n        }\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            const mediaRecorder = new MediaRecorder(stream);\n            mediaRecorderRef.current = mediaRecorder;\n            const audioChunks = [];\n            mediaRecorder.ondataavailable = (event)=>{\n                audioChunks.push(event.data);\n            };\n            mediaRecorder.onstop = async ()=>{\n                const audioBlob = new Blob(audioChunks, {\n                    type: 'audio/webm'\n                });\n                try {\n                    const { transcript } = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.transcribeAudio)(audioBlob);\n                    setInput(transcript);\n                } catch (error) {\n                    console.error('Error transcribing audio:', error);\n                }\n                stream.getTracks().forEach((track)=>track.stop());\n            };\n            mediaRecorder.start();\n            setIsRecording(true);\n        } catch (error) {\n            console.error('Error accessing microphone:', error);\n        }\n    };\n    const clearChat = ()=>{\n        setMessages([\n            {\n                role: 'system',\n                content: \"Xin chào, hãy tiếp tục với Medicagent nhé!\"\n            }\n        ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-[#F4F6F8] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"w-72 h-full py-3 px-3 hidden md:flex md:flex-col border-[#2A9DF4] border-r border-l-0 border-t-0 border-b-0 rounded-none bg-white shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-3 flex items-center gap-2 text-[#2A9DF4] px-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-robot\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            \"Medicagent\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-[#2A9DF4] scrollbar-track-transparent\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-[#F4F6F8]/50 p-2 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-1 flex items-center gap-2 text-[#2A9DF4] text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-comment-medical\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"T\\xe1c vụ hội thoại\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-xs space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex flex-col gap-1 hover:text-[#2A9DF4] transition-colors p-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-comment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Conversation Agent\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pl-5 text-[10px] text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Hội thoại chung\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex flex-col gap-1 hover:text-[#2A9DF4] transition-colors p-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-database\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Medical RAG Agent\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pl-5 text-[10px] text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Truy xuất th\\xf4ng tin y khoa: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Ph\\xe2n t\\xedch PDF dựa tr\\xean Docling\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Nh\\xfang nội dung định dạng markdown\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Ph\\xe2n đoạn ngữ nghĩa dựa tr\\xean LLM\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• T\\xecm kiếm lai Qdrant Vector DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex flex-col gap-1 hover:text-[#2A9DF4] transition-colors p-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-search\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Web Search Agent\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pl-5 text-[10px] text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"T\\xecm kiếm th\\xf4ng tin: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• PubMed Search: nghi\\xean cứu y học\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Tavily Search: t\\xecm kiếm đa nguồn\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-[#F4F6F8]/50 p-2 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-1 flex items-center gap-2 text-[#2A9DF4] text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-camera\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"T\\xe1c vụ thị gi\\xe1c m\\xe1y t\\xednh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-xs space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex flex-col gap-1 hover:text-[#2A9DF4] transition-colors p-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-brain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Brain Tumor Agent\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pl-5 text-[10px] text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Ph\\xe2n loại h\\xecnh ảnh MRI n\\xe3o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Accuracy: 97.56%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex flex-col gap-1 hover:text-[#2A9DF4] transition-colors p-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-lungs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Chest Xray Agent\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pl-5 text-[10px] text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Nhận diện Covid-19 từ X-quang\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Accuracy: 97%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex flex-col gap-1 hover:text-[#2A9DF4] transition-colors p-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-allergies\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Skin Lesion Agent\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pl-5 text-[10px] text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Ph\\xe2n v\\xf9ng tổn thương da\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Dice Score: 0.784\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        className: \"w-full mt-2 bg-[#D9534F] hover:bg-[#F44336] text-white\",\n                        onClick: clearChat,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-3.5 w-3.5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            \"X\\xf3a Cuộc Hội Thoại\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col bg-white h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chatContainerRef,\n                        className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-white to-[#F4F6F8]\",\n                        children: [\n                            messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Message__WEBPACK_IMPORTED_MODULE_2__.Message, {\n                                    message: message,\n                                    onValidation: message.agent?.includes('HUMAN_VALIDATION') ? handleValidation : undefined\n                                }, index, false, {\n                                    fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)),\n                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-2 w-2 bg-[#2A9DF4] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-2 w-2 bg-[#2A9DF4] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-2 w-2 bg-[#2A9DF4] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-t border-[#F4F6F8] bg-white\",\n                        children: [\n                            selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2 relative inline-block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: selectedImage,\n                                        alt: \"Preview\",\n                                        className: \"h-16 w-16 object-cover rounded-md shadow-sm border border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600 transition-colors\",\n                                        onClick: ()=>setSelectedImage(null),\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        ref: fileInputRef,\n                                        className: \"hidden\",\n                                        accept: \"image/*\",\n                                        onChange: handleImageUpload\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>fileInputRef.current?.click(),\n                                        className: \"text-[#2A9DF4] border-[#2A9DF4] hover:bg-[#2A9DF4] hover:text-white transition-all h-9 w-9\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: toggleRecording,\n                                        className: isRecording ? 'bg-[#A3D9A5] text-white border-[#A3D9A5] hover:bg-[#8FCC91] h-9 w-9' : 'text-[#2A9DF4] border-[#2A9DF4] hover:bg-[#2A9DF4] hover:text-white transition-all h-9 w-9',\n                                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: input,\n                                        onChange: (e)=>setInput(e.target.value),\n                                        placeholder: \"Nhập c\\xe2u hỏi...\",\n                                        className: \"flex-1 border-[#2A9DF4] focus-visible:ring-[#2A9DF4] rounded-md resize-none h-9 min-h-[36px] py-2 px-3\",\n                                        onKeyDown: (e)=>{\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                handleSendMessage();\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleSendMessage,\n                                        disabled: isProcessing || !input.trim() && !selectedImage,\n                                        className: \"bg-[#2A9DF4] hover:bg-[#1E8CE3] text-white h-9\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Chat.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/Chat.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/Message.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/Message.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Message: () => (/* binding */ Message)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/avatar */ \"(ssr)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Play,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Play,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var marked__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! marked */ \"(ssr)/./node_modules/marked/lib/marked.esm.js\");\n/* __next_internal_client_entry_do_not_use__ Message auto */ \n\n\n\n\n\n\n\n\nfunction Message({ message, onValidation }) {\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // System is now English-only\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const [validationComments, setValidationComments] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('');\n    const handlePlayAudio = async ()=>{\n        if (isGenerating || isPlaying) return;\n        try {\n            setIsGenerating(true);\n            // Log the content that's being sent for speech generation\n            console.log(\"Sending for speech generation:\", message.content);\n            // Check if the content is just markdown\n            const stripMarkdown = (text)=>{\n                // First, handle basic markdown\n                let cleaned = text.replace(/\\*\\*(.*?)\\*\\*/g, '$1') // Remove bold\n                .replace(/\\*(.*?)\\*/g, '$1') // Remove italic\n                .replace(/\\[(.*?)\\]\\((.*?)\\)/g, '$1') // Remove links\n                .replace(/#{1,6}\\s?(.*?)$/gm, '$1') // Remove headings\n                .replace(/`{3}.*?\\n([\\s\\S]*?)`{3}/g, '$1') // Remove code blocks\n                .replace(/`(.*?)`/g, '$1') // Remove inline code\n                .replace(/\\n{2,}/g, '\\n') // Replace multiple newlines with single\n                .trim();\n                // Handle special cases for Vietnamese punctuation\n                // Sometimes \"Phẩy\" might be a result of a punctuation mark being treated as a word\n                cleaned = cleaned.replace(/\\s+\\,/g, ',') // Remove space before comma\n                .replace(/\\s+\\./g, '.') // Remove space before period\n                .replace(/\\,\\s+/g, ', ') // Ensure space after comma\n                .replace(/\\.\\s+/g, '. ') // Ensure space after period\n                .replace(/\\s+\\?/g, '?') // Remove space before question mark\n                .replace(/\\?\\s+/g, '? ') // Ensure space after question mark\n                .replace(/\\s+\\!/g, '!') // Remove space before exclamation mark\n                .replace(/\\!\\s+/g, '! ') // Ensure space after exclamation mark\n                .replace(/\\s+\\;/g, ';') // Remove space before semicolon\n                .replace(/\\;\\s+/g, '; '); // Ensure space after semicolon\n                // Check if the text is just \"Phẩy\" or starts with it\n                if (cleaned === \"Phẩy\" || cleaned === \"phẩy\" || cleaned.startsWith(\"Phẩy,\") || cleaned.startsWith(\"phẩy,\")) {\n                    console.warn(\"Detected standalone 'Phẩy' in text, might be a TTS issue\");\n                }\n                return cleaned;\n            };\n            // Get and clean the text\n            let cleanedText = stripMarkdown(message.content);\n            console.log(\"Cleaned text:\", cleanedText);\n            // Additional check to prevent lone punctuation\n            if (cleanedText.trim().length < 2 || /^[,.?!;:]$/.test(cleanedText.trim())) {\n                console.warn(\"Text too short or just punctuation, adding default message\");\n                cleanedText = \"Sorry, this text cannot be spoken.\";\n            }\n            try {\n                const audioBlob = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.generateSpeech)({\n                    text: cleanedText\n                });\n                // Check if we received a valid audio blob\n                if (!audioBlob || audioBlob.size < 100) {\n                    console.error(\"Received invalid audio response\", audioBlob);\n                    throw new Error(\"Invalid audio response\");\n                }\n                const audioUrl = URL.createObjectURL(audioBlob);\n                if (audioRef.current) {\n                    audioRef.current.src = audioUrl;\n                    audioRef.current.playbackRate = 1.2; // Default speed is 1.2x\n                    // Set up error handling for audio playback\n                    audioRef.current.onerror = (e)=>{\n                        console.error(\"Audio playback error:\", e);\n                        setIsPlaying(false);\n                        setIsGenerating(false);\n                        URL.revokeObjectURL(audioUrl);\n                    };\n                    // Start playback\n                    audioRef.current.play().then(()=>{\n                        setIsPlaying(true);\n                    }).catch((err)=>{\n                        console.error(\"Failed to play audio:\", err);\n                        setIsPlaying(false);\n                        URL.revokeObjectURL(audioUrl);\n                    });\n                }\n            } catch (error) {\n                console.error('Failed to generate or play speech:', error);\n                // Show a user-friendly error message\n                alert(\"Unable to speak this text.\");\n            }\n        } catch (error) {\n            console.error('Failed to generate speech:', error);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const handlePauseAudio = ()=>{\n        if (audioRef.current && isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n            // Clear the audio source to stop it completely\n            if (audioRef.current.src) {\n                URL.revokeObjectURL(audioRef.current.src);\n                audioRef.current.src = '';\n            }\n        }\n    };\n    const handleAudioEnded = ()=>{\n        setIsPlaying(false);\n        if (audioRef.current) {\n            URL.revokeObjectURL(audioRef.current.src);\n        }\n    };\n    const handleValidation = async (validation)=>{\n        if (onValidation) {\n            await onValidation(validation, validationComments);\n        }\n    };\n    // Language toggle removed - system is English-only\n    // Configure marked options for better table rendering\n    marked__WEBPACK_IMPORTED_MODULE_7__.marked.setOptions({\n        breaks: true,\n        gfm: true\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex gap-4 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`,\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                className: \"h-8 w-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-primary text-primary-foreground flex h-full w-full items-center justify-center\",\n                    children: \"AI\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: `max-w-[80%] p-4 ${message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`,\n                children: [\n                    message.agent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-922ef5f506910f61\" + \" \" + \"text-xs font-semibold mb-2\",\n                        children: message.agent\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        dangerouslySetInnerHTML: {\n                            __html: (0,marked__WEBPACK_IMPORTED_MODULE_7__.marked)(message.content)\n                        },\n                        className: \"jsx-922ef5f506910f61\" + \" \" + \"prose prose-sm dark:prose-invert max-w-none overflow-x-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"922ef5f506910f61\",\n                        children: \".prose table{width:100%;border-collapse:collapse;margin:1rem 0;font-size:.875rem}.prose th{background-color:rgba(0,0,0,.1);font-weight:600;text-align:left;padding:.75rem;border:1px solid rgba(0,0,0,.1)}.prose td{padding:.75rem;border:1px solid rgba(0,0,0,.1)}.prose tr:nth-child(even){background-color:rgba(0,0,0,.05)}.prose img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}.prose h1,.prose h2,.prose h3,.prose h4,.prose h5,.prose h6{margin-top:1.5rem;margin-bottom:1rem;font-weight:600}.prose ul,.prose ol{margin:1rem 0;padding-left:1.5rem}.prose li{margin:.5rem 0}.prose blockquote{border-left:4px solid rgba(0,0,0,.1);padding-left:1rem;margin:1rem 0;font-style:italic}.prose code{background-color:rgba(0,0,0,.1);padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-size:.875em}.prose pre{background-color:rgba(0,0,0,.1);padding:1rem;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;overflow-x:auto}.prose a{color:#2563eb;text-decoration:underline}.prose a:hover{color:#1d4ed8}\"\n                    }, void 0, false, void 0, this),\n                    message.image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-922ef5f506910f61\" + \" \" + \"mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: message.image,\n                            alt: \"Image\",\n                            style: {\n                                maxHeight: '300px',\n                                objectFit: 'contain'\n                            },\n                            className: \"jsx-922ef5f506910f61\" + \" \" + \"max-w-full rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this),\n                    message.resultImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-922ef5f506910f61\" + \" \" + \"mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: message.resultImage,\n                            alt: \"Result Image\",\n                            style: {\n                                maxHeight: '300px',\n                                objectFit: 'contain'\n                            },\n                            className: \"jsx-922ef5f506910f61\" + \" \" + \"max-w-full rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, this),\n                    message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-922ef5f506910f61\" + \" \" + \"mt-2 flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handlePlayAudio,\n                                disabled: isGenerating || isPlaying,\n                                title: \"Play\",\n                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-922ef5f506910f61\" + \" \" + \"animate-spin\",\n                                    children: \"⌛\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handlePauseAudio,\n                                title: \"Stop\",\n                                className: \"bg-red-50 hover:bg-red-100 border-red-200 text-red-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this),\n                    message.agent?.includes('HUMAN_VALIDATION') && onValidation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-922ef5f506910f61\" + \" \" + \"mt-4 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-922ef5f506910f61\" + \" \" + \"text-sm font-medium\",\n                                children: \"Do you agree with this result?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-922ef5f506910f61\" + \" \" + \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleValidation('yes'),\n                                        children: \"Yes/C\\xf3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleValidation('no'),\n                                        children: \"No/Kh\\xf4ng\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                placeholder: \"Add comments (optional)\",\n                                value: validationComments,\n                                onChange: (e)=>setValidationComments(e.target.value),\n                                className: \"jsx-922ef5f506910f61\" + \" \" + \"w-full p-2 text-sm border rounded\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                className: \"h-8 w-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-secondary text-secondary-foreground flex h-full w-full items-center justify-center\",\n                    children: \"U\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: audioRef,\n                onEnded: handleAudioEnded,\n                onPause: ()=>setIsPlaying(false),\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\chat\\\\Message.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/Message.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/avatar.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/avatar.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarImage,AvatarFallback auto */ \n\n\n\nfunction Avatar({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"avatar\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex size-8 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction AvatarImage({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        \"data-slot\": \"avatar-image\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square size-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction AvatarFallback({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        \"data-slot\": \"avatar-fallback\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-muted flex size-full items-center justify-center rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/textarea.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/textarea.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Textarea({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        \"data-slot\": \"textarea\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Medicagent\\\\frontend\\\\src\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS90ZXh0YXJlYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLFNBQVNFLFNBQVMsRUFBRUMsU0FBUyxFQUFFLEdBQUdDLE9BQXlDO0lBQ3pFLHFCQUNFLDhEQUFDQztRQUNDQyxhQUFVO1FBQ1ZILFdBQVdGLDhDQUFFQSxDQUNYLHVjQUNBRTtRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBRW1CIiwic291cmNlcyI6WyJEOlxcTWVkaWNhZ2VudFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcdWlcXHRleHRhcmVhLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxyXG5cclxuZnVuY3Rpb24gVGV4dGFyZWEoeyBjbGFzc05hbWUsIC4uLnByb3BzIH06IFJlYWN0LkNvbXBvbmVudFByb3BzPFwidGV4dGFyZWFcIj4pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPHRleHRhcmVhXHJcbiAgICAgIGRhdGEtc2xvdD1cInRleHRhcmVhXCJcclxuICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICBcImJvcmRlci1pbnB1dCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpib3JkZXItcmluZyBmb2N1cy12aXNpYmxlOnJpbmctcmluZy81MCBhcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS8yMCBkYXJrOmFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzQwIGFyaWEtaW52YWxpZDpib3JkZXItZGVzdHJ1Y3RpdmUgZGFyazpiZy1pbnB1dC8zMCBmbGV4IGZpZWxkLXNpemluZy1jb250ZW50IG1pbi1oLTE2IHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBiZy10cmFuc3BhcmVudCBweC0zIHB5LTIgdGV4dC1iYXNlIHNoYWRvdy14cyB0cmFuc2l0aW9uLVtjb2xvcixib3gtc2hhZG93XSBvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLVszcHhdIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwIG1kOnRleHQtc21cIixcclxuICAgICAgICBjbGFzc05hbWVcclxuICAgICAgKX1cclxuICAgICAgey4uLnByb3BzfVxyXG4gICAgLz5cclxuICApXHJcbn1cclxuXHJcbmV4cG9ydCB7IFRleHRhcmVhIH1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJUZXh0YXJlYSIsImNsYXNzTmFtZSIsInByb3BzIiwidGV4dGFyZWEiLCJkYXRhLXNsb3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateSpeech: () => (/* binding */ generateSpeech),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage),\n/* harmony export */   sendValidation: () => (/* binding */ sendValidation),\n/* harmony export */   transcribeAudio: () => (/* binding */ transcribeAudio),\n/* harmony export */   uploadImage: () => (/* binding */ uploadImage)\n/* harmony export */ });\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\nasync function sendChatMessage(message) {\n    const response = await fetch(`${API_BASE_URL}/api/chat`, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n            query: message,\n            conversation_history: []\n        }),\n        credentials: 'include'\n    });\n    if (!response.ok) {\n        throw new Error('Failed to send message');\n    }\n    return response.json();\n}\nasync function uploadImage(image, text = '') {\n    const formData = new FormData();\n    formData.append('image', image);\n    formData.append('text', text);\n    const response = await fetch(`${API_BASE_URL}/api/upload`, {\n        method: 'POST',\n        body: formData,\n        credentials: 'include'\n    });\n    if (!response.ok) {\n        throw new Error('Failed to upload image');\n    }\n    const data = await response.json();\n    // Ensure result_image has the full URL if it exists\n    if (data.result_image && !data.result_image.startsWith('http')) {\n        data.result_image = `${API_BASE_URL}${data.result_image}`;\n    }\n    return data;\n}\nasync function sendValidation(validation, comments) {\n    const formData = new FormData();\n    formData.append('validation_result', validation);\n    formData.append('comments', comments);\n    const response = await fetch(`${API_BASE_URL}/api/validate`, {\n        method: 'POST',\n        body: formData,\n        credentials: 'include'\n    });\n    if (!response.ok) {\n        throw new Error('Failed to send validation');\n    }\n    return response.json();\n}\nasync function transcribeAudio(audio) {\n    console.log(`Preparing to transcribe audio: ${audio.size} bytes (English-only)`);\n    const formData = new FormData();\n    formData.append('audio', audio);\n    // System is English-only - no language parameter needed\n    console.log(`Sending request to ${API_BASE_URL}/api/transcribe`);\n    try {\n        const response = await fetch(`${API_BASE_URL}/api/transcribe`, {\n            method: 'POST',\n            body: formData\n        });\n        console.log(`Transcription response status: ${response.status}`);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(`Transcription failed: ${response.status}`, errorText);\n            throw new Error(`Failed to transcribe audio: ${response.status} ${errorText}`);\n        }\n        const data = await response.json();\n        console.log(`Transcription result:`, data);\n        return data;\n    } catch (error) {\n        console.error('Network or parsing error during transcription:', error);\n        throw error;\n    }\n}\nasync function generateSpeech(request) {\n    const response = await fetch(`${API_BASE_URL}/api/generate-speech`, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(request)\n    });\n    if (!response.ok) {\n        throw new Error('Failed to generate speech');\n    }\n    return response.blob();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXE1lZGljYWdlbnRcXGZyb250ZW5kXFxzcmNcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxyXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xyXG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcclxufVxyXG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/@swc","vendor-chunks/use-sync-external-store","vendor-chunks/styled-jsx","vendor-chunks/tailwind-merge","vendor-chunks/marked","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CMedicagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMedicagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();